{"time":"2025-08-03T15:51:36.379599806+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:51:36.503888865+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:51:36.5041233+08:00","level":"INFO","msg":"stream: created new stream","id":"68lw8elk"}
{"time":"2025-08-03T15:51:36.504154789+08:00","level":"INFO","msg":"stream: started","id":"68lw8elk"}
{"time":"2025-08-03T15:51:36.504217821+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"68lw8elk"}
{"time":"2025-08-03T15:51:36.504266097+08:00","level":"INFO","msg":"sender: started","stream_id":"68lw8elk"}
{"time":"2025-08-03T15:51:36.504334665+08:00","level":"INFO","msg":"handler: started","stream_id":"68lw8elk"}
{"time":"2025-08-03T15:51:36.505875319+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
