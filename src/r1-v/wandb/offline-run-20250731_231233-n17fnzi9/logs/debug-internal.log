{"time":"2025-07-31T23:12:33.485476962+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T23:12:33.615566832+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T23:12:33.61588829+08:00","level":"INFO","msg":"stream: created new stream","id":"n17fnzi9"}
{"time":"2025-07-31T23:12:33.615933465+08:00","level":"INFO","msg":"stream: started","id":"n17fnzi9"}
{"time":"2025-07-31T23:12:33.616068587+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"n17fnzi9"}
{"time":"2025-07-31T23:12:33.616122921+08:00","level":"INFO","msg":"handler: started","stream_id":"n17fnzi9"}
{"time":"2025-07-31T23:12:33.616206868+08:00","level":"INFO","msg":"sender: started","stream_id":"n17fnzi9"}
{"time":"2025-07-31T23:12:33.617411376+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-07-31T23:13:33.712104372+08:00","level":"INFO","msg":"stream: closing","id":"n17fnzi9"}
{"time":"2025-07-31T23:13:33.712499686+08:00","level":"INFO","msg":"handler: closed","stream_id":"n17fnzi9"}
{"time":"2025-07-31T23:13:33.712543618+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"n17fnzi9"}
{"time":"2025-07-31T23:13:33.712552999+08:00","level":"INFO","msg":"sender: closed","stream_id":"n17fnzi9"}
{"time":"2025-07-31T23:13:33.712654443+08:00","level":"INFO","msg":"stream: closed","id":"n17fnzi9"}
