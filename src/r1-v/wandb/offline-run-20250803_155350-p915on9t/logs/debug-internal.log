{"time":"2025-08-03T15:53:50.455174675+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:53:50.583250171+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:53:50.583517581+08:00","level":"INFO","msg":"stream: created new stream","id":"p915on9t"}
{"time":"2025-08-03T15:53:50.583550408+08:00","level":"INFO","msg":"stream: started","id":"p915on9t"}
{"time":"2025-08-03T15:53:50.583663143+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"p915on9t"}
{"time":"2025-08-03T15:53:50.583716303+08:00","level":"INFO","msg":"sender: started","stream_id":"p915on9t"}
{"time":"2025-08-03T15:53:50.58374868+08:00","level":"INFO","msg":"handler: started","stream_id":"p915on9t"}
{"time":"2025-08-03T15:53:50.584922543+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
