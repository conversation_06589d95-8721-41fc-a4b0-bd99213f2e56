{"time":"2025-08-05T13:48:37.911980894+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:48:38.050037954+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:48:38.050413589+08:00","level":"INFO","msg":"stream: created new stream","id":"6pcgxl9e"}
{"time":"2025-08-05T13:48:38.050463373+08:00","level":"INFO","msg":"stream: started","id":"6pcgxl9e"}
{"time":"2025-08-05T13:48:38.050565394+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"6pcgxl9e"}
{"time":"2025-08-05T13:48:38.050582971+08:00","level":"INFO","msg":"handler: started","stream_id":"6pcgxl9e"}
{"time":"2025-08-05T13:48:38.050681536+08:00","level":"INFO","msg":"sender: started","stream_id":"6pcgxl9e"}
{"time":"2025-08-05T13:48:38.051735938+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T17:29:51.946777932+08:00","level":"INFO","msg":"stream: closing","id":"6pcgxl9e"}
{"time":"2025-08-05T17:29:51.947127168+08:00","level":"INFO","msg":"handler: closed","stream_id":"6pcgxl9e"}
{"time":"2025-08-05T17:29:51.947158055+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"6pcgxl9e"}
{"time":"2025-08-05T17:29:51.947194921+08:00","level":"INFO","msg":"sender: closed","stream_id":"6pcgxl9e"}
{"time":"2025-08-05T17:29:51.947374213+08:00","level":"INFO","msg":"stream: closed","id":"6pcgxl9e"}
