{"time":"2025-08-01T13:52:42.771511986+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T13:52:42.900810139+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T13:52:42.901096472+08:00","level":"INFO","msg":"stream: created new stream","id":"6sbr218o"}
{"time":"2025-08-01T13:52:42.901138519+08:00","level":"INFO","msg":"stream: started","id":"6sbr218o"}
{"time":"2025-08-01T13:52:42.901241154+08:00","level":"INFO","msg":"sender: started","stream_id":"6sbr218o"}
{"time":"2025-08-01T13:52:42.90123084+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"6sbr218o"}
{"time":"2025-08-01T13:52:42.9013219+08:00","level":"INFO","msg":"handler: started","stream_id":"6sbr218o"}
{"time":"2025-08-01T13:52:42.902309085+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-02T18:04:44.562547981+08:00","level":"INFO","msg":"stream: closing","id":"6sbr218o"}
{"time":"2025-08-02T18:04:44.562958492+08:00","level":"INFO","msg":"handler: closed","stream_id":"6sbr218o"}
{"time":"2025-08-02T18:04:44.563045001+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"6sbr218o"}
{"time":"2025-08-02T18:04:44.563072794+08:00","level":"INFO","msg":"sender: closed","stream_id":"6sbr218o"}
{"time":"2025-08-02T18:04:44.563169902+08:00","level":"INFO","msg":"stream: closed","id":"6sbr218o"}
