{"time":"2025-07-31T23:34:56.95029793+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T23:34:57.089380912+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T23:34:57.089725178+08:00","level":"INFO","msg":"stream: created new stream","id":"usfxx2vz"}
{"time":"2025-07-31T23:34:57.089770995+08:00","level":"INFO","msg":"stream: started","id":"usfxx2vz"}
{"time":"2025-07-31T23:34:57.089858885+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"usfxx2vz"}
{"time":"2025-07-31T23:34:57.089933155+08:00","level":"INFO","msg":"handler: started","stream_id":"usfxx2vz"}
{"time":"2025-07-31T23:34:57.090047408+08:00","level":"INFO","msg":"sender: started","stream_id":"usfxx2vz"}
{"time":"2025-07-31T23:34:57.09120301+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
