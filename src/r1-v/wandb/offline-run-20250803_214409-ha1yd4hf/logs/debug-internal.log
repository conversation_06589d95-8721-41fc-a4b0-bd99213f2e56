{"time":"2025-08-03T21:44:09.433733318+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T21:44:09.565555857+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T21:44:09.565833692+08:00","level":"INFO","msg":"stream: created new stream","id":"ha1yd4hf"}
{"time":"2025-08-03T21:44:09.565865603+08:00","level":"INFO","msg":"stream: started","id":"ha1yd4hf"}
{"time":"2025-08-03T21:44:09.565970155+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ha1yd4hf"}
{"time":"2025-08-03T21:44:09.566074213+08:00","level":"INFO","msg":"handler: started","stream_id":"ha1yd4hf"}
{"time":"2025-08-03T21:44:09.566147973+08:00","level":"INFO","msg":"sender: started","stream_id":"ha1yd4hf"}
{"time":"2025-08-03T21:44:09.566940187+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T21:46:02.731305352+08:00","level":"INFO","msg":"stream: closing","id":"ha1yd4hf"}
{"time":"2025-08-03T21:46:02.731593807+08:00","level":"INFO","msg":"handler: closed","stream_id":"ha1yd4hf"}
{"time":"2025-08-03T21:46:02.731631414+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ha1yd4hf"}
{"time":"2025-08-03T21:46:02.731660683+08:00","level":"INFO","msg":"sender: closed","stream_id":"ha1yd4hf"}
{"time":"2025-08-03T21:46:02.731779896+08:00","level":"INFO","msg":"stream: closed","id":"ha1yd4hf"}
