{"time":"2025-08-01T11:39:12.312079625+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T11:39:12.44357442+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T11:39:12.443801335+08:00","level":"INFO","msg":"stream: created new stream","id":"tntozju9"}
{"time":"2025-08-01T11:39:12.443831669+08:00","level":"INFO","msg":"stream: started","id":"tntozju9"}
{"time":"2025-08-01T11:39:12.443931461+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"tntozju9"}
{"time":"2025-08-01T11:39:12.444049543+08:00","level":"INFO","msg":"sender: started","stream_id":"tntozju9"}
{"time":"2025-08-01T11:39:12.444070849+08:00","level":"INFO","msg":"handler: started","stream_id":"tntozju9"}
{"time":"2025-08-01T11:39:12.44559419+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
