{"time":"2025-08-04T10:55:45.173424566+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T10:55:45.310698206+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T10:55:45.31105231+08:00","level":"INFO","msg":"stream: created new stream","id":"428x3jqf"}
{"time":"2025-08-04T10:55:45.31110112+08:00","level":"INFO","msg":"stream: started","id":"428x3jqf"}
{"time":"2025-08-04T10:55:45.3111939+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"428x3jqf"}
{"time":"2025-08-04T10:55:45.31122783+08:00","level":"INFO","msg":"handler: started","stream_id":"428x3jqf"}
{"time":"2025-08-04T10:55:45.31128488+08:00","level":"INFO","msg":"sender: started","stream_id":"428x3jqf"}
{"time":"2025-08-04T10:55:45.312432085+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
