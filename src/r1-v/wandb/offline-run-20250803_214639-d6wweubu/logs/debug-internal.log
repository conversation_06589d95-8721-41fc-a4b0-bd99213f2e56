{"time":"2025-08-03T21:46:39.612493965+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T21:46:39.74196868+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T21:46:39.742350226+08:00","level":"INFO","msg":"stream: created new stream","id":"d6wweubu"}
{"time":"2025-08-03T21:46:39.74237996+08:00","level":"INFO","msg":"stream: started","id":"d6wweubu"}
{"time":"2025-08-03T21:46:39.742456259+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"d6wweubu"}
{"time":"2025-08-03T21:46:39.742539496+08:00","level":"INFO","msg":"handler: started","stream_id":"d6wweubu"}
{"time":"2025-08-03T21:46:39.742591715+08:00","level":"INFO","msg":"sender: started","stream_id":"d6wweubu"}
{"time":"2025-08-03T21:46:39.743579262+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
