#!/usr/bin/env python3
import json

def split_dataset_by_source():
    input_file = "/data/wuyang/merged_dataset_with_difficulty.json"
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    mafw_data = []
    dfew_data = []
    
    for sample in data:
        video_path = sample['video']
        if '/MAFW/' in video_path:
            mafw_data.append(sample)
        elif '/DFEW/' in video_path:
            dfew_data.append(sample)
    
    # 保存MAFW数据集
    mafw_output = "/data/wuyang/mafw_dataset_with_difficulty.json"
    with open(mafw_output, 'w', encoding='utf-8') as f:
        json.dump(mafw_data, f, ensure_ascii=False, indent=2)
    
    # 保存DFEW数据集
    dfew_output = "/data/wuyang/dfew_dataset_with_difficulty.json"
    with open(dfew_output, 'w', encoding='utf-8') as f:
        json.dump(dfew_data, f, ensure_ascii=False, indent=2)
    
    print(f"MAFW samples: {len(mafw_data)}")
    print(f"DFEW samples: {len(dfew_data)}")
    print(f"Total samples: {len(data)}")
    print(f"MAFW saved to: {mafw_output}")
    print(f"DFEW saved to: {dfew_output}")

if __name__ == "__main__":
    split_dataset_by_source()
