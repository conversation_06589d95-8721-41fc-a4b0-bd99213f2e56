{"time":"2025-08-01T13:44:01.100706236+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T13:44:01.230531646+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T13:44:01.23078234+08:00","level":"INFO","msg":"stream: created new stream","id":"8qoc2oet"}
{"time":"2025-08-01T13:44:01.230814768+08:00","level":"INFO","msg":"stream: started","id":"8qoc2oet"}
{"time":"2025-08-01T13:44:01.230892058+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"8qoc2oet"}
{"time":"2025-08-01T13:44:01.230971581+08:00","level":"INFO","msg":"sender: started","stream_id":"8qoc2oet"}
{"time":"2025-08-01T13:44:01.230985886+08:00","level":"INFO","msg":"handler: started","stream_id":"8qoc2oet"}
{"time":"2025-08-01T13:44:01.231871309+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-01T13:44:57.3178327+08:00","level":"INFO","msg":"stream: closing","id":"8qoc2oet"}
{"time":"2025-08-01T13:44:57.318192429+08:00","level":"INFO","msg":"handler: closed","stream_id":"8qoc2oet"}
{"time":"2025-08-01T13:44:57.318228589+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"8qoc2oet"}
{"time":"2025-08-01T13:44:57.318235979+08:00","level":"INFO","msg":"sender: closed","stream_id":"8qoc2oet"}
{"time":"2025-08-01T13:44:57.318382959+08:00","level":"INFO","msg":"stream: closed","id":"8qoc2oet"}
