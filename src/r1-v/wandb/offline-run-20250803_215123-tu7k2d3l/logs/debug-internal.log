{"time":"2025-08-03T21:51:23.832456977+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T21:51:23.958665403+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T21:51:23.959061293+08:00","level":"INFO","msg":"stream: created new stream","id":"tu7k2d3l"}
{"time":"2025-08-03T21:51:23.95910752+08:00","level":"INFO","msg":"stream: started","id":"tu7k2d3l"}
{"time":"2025-08-03T21:51:23.959203022+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"tu7k2d3l"}
{"time":"2025-08-03T21:51:23.959263637+08:00","level":"INFO","msg":"handler: started","stream_id":"tu7k2d3l"}
{"time":"2025-08-03T21:51:23.959348621+08:00","level":"INFO","msg":"sender: started","stream_id":"tu7k2d3l"}
{"time":"2025-08-03T21:51:23.960245292+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
