#!/usr/bin/env python3
import json
import re
from typing import Dict, List

def extract_answer(response: str) -> str:
    """从response中提取答案"""
    # 查找<answer>标签中的内容
    answer_match = re.search(r'<answer>(.*?)</answer>', response, re.IGNORECASE | re.DOTALL)
    if answer_match:
        return answer_match.group(1).strip()
    return ""

def load_prediction_results(prediction_file: str) -> Dict[str, str]:
    """加载预测结果文件，返回video_path到预测结果的映射"""
    video_predictions = {}
    
    with open(prediction_file, 'r', encoding='utf-8') as f:
        predictions = json.load(f)
    
    for pred in predictions:
        video_path = pred['video_path']
        gt = pred['gt']
        response = pred['response']
        predicted = extract_answer(response)
        
        # 判断预测是否正确
        is_correct = (gt.lower() == predicted.lower())
        difficulty = "easy" if is_correct else "hard"
        
        video_predictions[video_path] = difficulty
    
    return video_predictions

def add_difficulty_to_dataset(dataset_file: str, prediction_file: str, output_file: str):
    """为数据集添加difficulty标签"""
    # 加载预测结果
    print("Loading prediction results...")
    video_predictions = load_prediction_results(prediction_file)
    print(f"Loaded {len(video_predictions)} prediction results")
    
    # 加载原始数据集
    print("Loading original dataset...")
    with open(dataset_file, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    print(f"Loaded {len(dataset)} samples from dataset")
    
    # 为每个样本添加difficulty标签
    matched_count = 0
    unmatched_count = 0
    
    for sample in dataset:
        video_path = sample['video']
        
        if video_path in video_predictions:
            sample['difficulty'] = video_predictions[video_path]
            matched_count += 1
        else:
            # 如果没有找到对应的预测结果，标记为未知
            sample['difficulty'] = "unknown"
            unmatched_count += 1
            print(f"Warning: No prediction found for {video_path}")
    
    print(f"Matched: {matched_count}, Unmatched: {unmatched_count}")
    
    # 保存结果
    print(f"Saving results to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    print("Done!")
    
    # 统计difficulty分布
    difficulty_counts = {}
    for sample in dataset:
        diff = sample['difficulty']
        difficulty_counts[diff] = difficulty_counts.get(diff, 0) + 1
    
    print("\nDifficulty distribution:")
    for diff, count in difficulty_counts.items():
        print(f"  {diff}: {count}")

if __name__ == "__main__":
    dataset_file = "/data/wuyang/merged_dataset.json"
    prediction_file = "/data/wuyang/PLM/EMER-SFT-0.5B_emotion_250805020820.json"
    output_file = "/data/wuyang/merged_dataset_with_difficulty.json"
    
    add_difficulty_to_dataset(dataset_file, prediction_file, output_file)
