{"time":"2025-08-05T13:44:13.532626619+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:44:13.670554056+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:44:13.671314863+08:00","level":"INFO","msg":"stream: created new stream","id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:13.671366503+08:00","level":"INFO","msg":"stream: started","id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:13.671460081+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:13.671518497+08:00","level":"INFO","msg":"sender: started","stream_id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:13.671603391+08:00","level":"INFO","msg":"handler: started","stream_id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:13.67278283+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T13:44:51.732486009+08:00","level":"INFO","msg":"stream: closing","id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:51.732749822+08:00","level":"INFO","msg":"handler: closed","stream_id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:51.7327732+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:51.732798177+08:00","level":"INFO","msg":"sender: closed","stream_id":"kzw6o4yb"}
{"time":"2025-08-05T13:44:51.732844919+08:00","level":"INFO","msg":"stream: closed","id":"kzw6o4yb"}
