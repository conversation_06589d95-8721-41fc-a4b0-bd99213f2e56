{"time":"2025-08-05T13:46:40.097375807+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:46:40.22656385+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:46:40.226846716+08:00","level":"INFO","msg":"stream: created new stream","id":"afwnanxv"}
{"time":"2025-08-05T13:46:40.226883206+08:00","level":"INFO","msg":"stream: started","id":"afwnanxv"}
{"time":"2025-08-05T13:46:40.226986023+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"afwnanxv"}
{"time":"2025-08-05T13:46:40.227066865+08:00","level":"INFO","msg":"sender: started","stream_id":"afwnanxv"}
{"time":"2025-08-05T13:46:40.227112953+08:00","level":"INFO","msg":"handler: started","stream_id":"afwnanxv"}
{"time":"2025-08-05T13:46:40.228254891+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T13:46:52.246907122+08:00","level":"INFO","msg":"stream: closing","id":"afwnanxv"}
{"time":"2025-08-05T13:46:52.24738088+08:00","level":"INFO","msg":"handler: closed","stream_id":"afwnanxv"}
{"time":"2025-08-05T13:46:52.247411524+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"afwnanxv"}
{"time":"2025-08-05T13:46:52.24741809+08:00","level":"INFO","msg":"sender: closed","stream_id":"afwnanxv"}
{"time":"2025-08-05T13:46:52.247538237+08:00","level":"INFO","msg":"stream: closed","id":"afwnanxv"}
