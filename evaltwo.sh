export PYTHONPATH=/data/wuyang/R1-Omni-main:/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1:$PYTHONPATH

echo "开始执行任务4000..."
python -m torch.distributed.launch --use_env --master_port=29503 --nproc_per_node 4 --nnodes 1 \
    humanomni/eval/eval_mafw_dfew.py \
    --checkpoint /data/wuyang/R1-Omni-main/Outputs/eval_test/checkpoint-4000 \
    --dataset  emotion

echo "开始执行任务3000..."
python -m torch.distributed.launch --use_env --master_port=29503 --nproc_per_node 4 --nnodes 1 \
    humanomni/eval/eval_mafw_dfew.py \
    --checkpoint /data/wuyang/R1-Omni-main/Outputs/eval_test/checkpoint-3000 \
    --dataset  emotion

echo "开始执行任务2000..."
python -m torch.distributed.launch --use_env --master_port=29503 --nproc_per_node 4 --nnodes 1 \
    humanomni/eval/eval_mafw_dfew.py \
    --checkpoint /data/wuyang/R1-Omni-main/Outputs/eval_test/checkpoint-2000 \
    --dataset  emotion

echo "开始执行任务1000..."
python -m torch.distributed.launch --use_env --master_port=29503 --nproc_per_node 4 --nnodes 1 \
    humanomni/eval/eval_mafw_dfew.py \
    --checkpoint /data/wuyang/R1-Omni-main/Outputs/eval_test/checkpoint-1000 \
    --dataset  emotion

echo "所有任务执行完毕！"