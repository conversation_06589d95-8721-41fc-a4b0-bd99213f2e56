{"time":"2025-08-01T11:43:49.121494243+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T11:43:49.249997221+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T11:43:49.250227115+08:00","level":"INFO","msg":"stream: created new stream","id":"al26zonm"}
{"time":"2025-08-01T11:43:49.25024892+08:00","level":"INFO","msg":"stream: started","id":"al26zonm"}
{"time":"2025-08-01T11:43:49.250353669+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"al26zonm"}
{"time":"2025-08-01T11:43:49.250406965+08:00","level":"INFO","msg":"handler: started","stream_id":"al26zonm"}
{"time":"2025-08-01T11:43:49.250493405+08:00","level":"INFO","msg":"sender: started","stream_id":"al26zonm"}
{"time":"2025-08-01T11:43:49.251537116+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-01T11:47:40.797147411+08:00","level":"INFO","msg":"stream: closing","id":"al26zonm"}
{"time":"2025-08-01T11:47:40.797503286+08:00","level":"INFO","msg":"handler: closed","stream_id":"al26zonm"}
{"time":"2025-08-01T11:47:40.797541048+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"al26zonm"}
{"time":"2025-08-01T11:47:40.79754443+08:00","level":"INFO","msg":"sender: closed","stream_id":"al26zonm"}
{"time":"2025-08-01T11:47:40.79764603+08:00","level":"INFO","msg":"stream: closed","id":"al26zonm"}
