{"time":"2025-08-01T13:10:25.839534246+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T13:10:25.97756166+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T13:10:25.97820428+08:00","level":"INFO","msg":"stream: created new stream","id":"69z29bud"}
{"time":"2025-08-01T13:10:25.978250264+08:00","level":"INFO","msg":"stream: started","id":"69z29bud"}
{"time":"2025-08-01T13:10:25.978317223+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"69z29bud"}
{"time":"2025-08-01T13:10:25.978399129+08:00","level":"INFO","msg":"handler: started","stream_id":"69z29bud"}
{"time":"2025-08-01T13:10:25.978481769+08:00","level":"INFO","msg":"sender: started","stream_id":"69z29bud"}
{"time":"2025-08-01T13:10:25.979305858+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-01T13:11:58.102527733+08:00","level":"INFO","msg":"stream: closing","id":"69z29bud"}
{"time":"2025-08-01T13:11:58.102848077+08:00","level":"INFO","msg":"handler: closed","stream_id":"69z29bud"}
{"time":"2025-08-01T13:11:58.102910961+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"69z29bud"}
{"time":"2025-08-01T13:11:58.102916644+08:00","level":"INFO","msg":"sender: closed","stream_id":"69z29bud"}
{"time":"2025-08-01T13:11:58.103033172+08:00","level":"INFO","msg":"stream: closed","id":"69z29bud"}
