{"time":"2025-08-04T10:54:33.171882098+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T10:54:33.308191127+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T10:54:33.308546335+08:00","level":"INFO","msg":"stream: created new stream","id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:33.308595747+08:00","level":"INFO","msg":"stream: started","id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:33.308698878+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:33.308759958+08:00","level":"INFO","msg":"handler: started","stream_id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:33.308814721+08:00","level":"INFO","msg":"sender: started","stream_id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:33.309694707+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T10:54:41.323592958+08:00","level":"INFO","msg":"stream: closing","id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:41.323813288+08:00","level":"INFO","msg":"handler: closed","stream_id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:41.323833461+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:41.323839475+08:00","level":"INFO","msg":"sender: closed","stream_id":"u7pqfxzj"}
{"time":"2025-08-04T10:54:41.323902194+08:00","level":"INFO","msg":"stream: closed","id":"u7pqfxzj"}
