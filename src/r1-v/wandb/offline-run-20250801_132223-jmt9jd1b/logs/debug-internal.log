{"time":"2025-08-01T13:22:23.339572495+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T13:22:23.474237736+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T13:22:23.474542536+08:00","level":"INFO","msg":"stream: created new stream","id":"jmt9jd1b"}
{"time":"2025-08-01T13:22:23.474588549+08:00","level":"INFO","msg":"stream: started","id":"jmt9jd1b"}
{"time":"2025-08-01T13:22:23.474614056+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"jmt9jd1b"}
{"time":"2025-08-01T13:22:23.474701172+08:00","level":"INFO","msg":"sender: started","stream_id":"jmt9jd1b"}
{"time":"2025-08-01T13:22:23.474774648+08:00","level":"INFO","msg":"handler: started","stream_id":"jmt9jd1b"}
{"time":"2025-08-01T13:22:23.475806464+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
