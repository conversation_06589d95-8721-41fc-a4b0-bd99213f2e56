{"time":"2025-08-01T11:53:44.354283986+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T11:53:44.489490656+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T11:53:44.489759748+08:00","level":"INFO","msg":"stream: created new stream","id":"wenqldzd"}
{"time":"2025-08-01T11:53:44.489802156+08:00","level":"INFO","msg":"stream: started","id":"wenqldzd"}
{"time":"2025-08-01T11:53:44.489902025+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wenqldzd"}
{"time":"2025-08-01T11:53:44.489907084+08:00","level":"INFO","msg":"handler: started","stream_id":"wenqldzd"}
{"time":"2025-08-01T11:53:44.489983817+08:00","level":"INFO","msg":"sender: started","stream_id":"wenqldzd"}
{"time":"2025-08-01T11:53:44.491249899+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
