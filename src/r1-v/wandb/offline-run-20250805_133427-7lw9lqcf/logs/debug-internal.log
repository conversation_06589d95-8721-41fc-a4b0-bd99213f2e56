{"time":"2025-08-05T13:34:27.304597089+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:34:27.440453845+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:34:27.440743064+08:00","level":"INFO","msg":"stream: created new stream","id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:27.44078377+08:00","level":"INFO","msg":"stream: started","id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:27.440883156+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:27.44104052+08:00","level":"INFO","msg":"handler: started","stream_id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:27.440955236+08:00","level":"INFO","msg":"sender: started","stream_id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:27.442236278+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T13:34:41.463889905+08:00","level":"INFO","msg":"stream: closing","id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:41.464109584+08:00","level":"INFO","msg":"handler: closed","stream_id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:41.464139565+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:41.464192109+08:00","level":"INFO","msg":"sender: closed","stream_id":"7lw9lqcf"}
{"time":"2025-08-05T13:34:41.464278736+08:00","level":"INFO","msg":"stream: closed","id":"7lw9lqcf"}
