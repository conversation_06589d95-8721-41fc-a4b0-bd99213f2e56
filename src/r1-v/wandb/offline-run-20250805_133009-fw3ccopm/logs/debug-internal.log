{"time":"2025-08-05T13:30:10.470612592+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:30:10.708903423+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:30:10.70919728+08:00","level":"INFO","msg":"stream: created new stream","id":"fw3ccopm"}
{"time":"2025-08-05T13:30:10.709243994+08:00","level":"INFO","msg":"stream: started","id":"fw3ccopm"}
{"time":"2025-08-05T13:30:10.709358774+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"fw3ccopm"}
{"time":"2025-08-05T13:30:10.709493699+08:00","level":"INFO","msg":"handler: started","stream_id":"fw3ccopm"}
{"time":"2025-08-05T13:30:10.709434954+08:00","level":"INFO","msg":"sender: started","stream_id":"fw3ccopm"}
{"time":"2025-08-05T13:30:10.710959859+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T13:30:33.745808426+08:00","level":"INFO","msg":"stream: closing","id":"fw3ccopm"}
{"time":"2025-08-05T13:30:33.746173203+08:00","level":"INFO","msg":"handler: closed","stream_id":"fw3ccopm"}
{"time":"2025-08-05T13:30:33.746218789+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"fw3ccopm"}
{"time":"2025-08-05T13:30:33.746234065+08:00","level":"INFO","msg":"sender: closed","stream_id":"fw3ccopm"}
{"time":"2025-08-05T13:30:33.746320858+08:00","level":"INFO","msg":"stream: closed","id":"fw3ccopm"}
