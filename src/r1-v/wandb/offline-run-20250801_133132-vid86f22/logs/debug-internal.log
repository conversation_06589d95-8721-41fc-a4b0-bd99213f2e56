{"time":"2025-08-01T13:31:32.657074426+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-01T13:31:32.788367256+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-01T13:31:32.788641801+08:00","level":"INFO","msg":"stream: created new stream","id":"vid86f22"}
{"time":"2025-08-01T13:31:32.788676514+08:00","level":"INFO","msg":"stream: started","id":"vid86f22"}
{"time":"2025-08-01T13:31:32.788774918+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"vid86f22"}
{"time":"2025-08-01T13:31:32.788838152+08:00","level":"INFO","msg":"sender: started","stream_id":"vid86f22"}
{"time":"2025-08-01T13:31:32.788863387+08:00","level":"INFO","msg":"handler: started","stream_id":"vid86f22"}
{"time":"2025-08-01T13:31:32.789968617+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-01T13:41:17.118557014+08:00","level":"INFO","msg":"stream: closing","id":"vid86f22"}
{"time":"2025-08-01T13:41:17.11883249+08:00","level":"INFO","msg":"handler: closed","stream_id":"vid86f22"}
{"time":"2025-08-01T13:41:17.118858849+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"vid86f22"}
{"time":"2025-08-01T13:41:17.118879912+08:00","level":"INFO","msg":"sender: closed","stream_id":"vid86f22"}
{"time":"2025-08-01T13:41:17.118948981+08:00","level":"INFO","msg":"stream: closed","id":"vid86f22"}
