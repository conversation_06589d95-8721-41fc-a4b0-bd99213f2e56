click==8.2.1
dill==0.3.8
setuptools==80.9.0
setuptools==78.1.1
httpcore==1.0.9
portalocker==3.2.0
markdown-it-py==3.0.0
inquirerpy==0.3.4
requests==2.32.4
codetiming==1.4.0
pycountry==24.6.1
hf-xet==1.1.5
anyio==4.9.0
flake8==7.3.0
rouge_score==0.1.2
nvidia-cufft-cu12==********
ipdb==0.13.13
h11==0.16.0
torchvision==0.21.0
torchaudio==2.6.0
yarl==1.20.1
hf_transfer==0.1.9
Pygments==2.19.2
liger_kernel==0.5.2
pure_eval==0.2.3
nvidia-nccl-cu12==2.21.5
trl==0.14.0
pyarrow==20.0.0
nvidia-ml-py==12.575.51
httpx==0.27.2
executing==2.2.0
ptyprocess==0.7.0
nltk==3.9.1
mdurl==0.1.2
flash-attn==2.7.3
pydantic_core==2.33.2
imageio==2.37.0
lxml==6.0.0
scipy==1.15.3
datasets==4.0.0
sacrebleu==2.5.1
opencv-python==*********
tabulate==0.9.0
nvidia-nvtx-cu12==12.4.127
traitlets==5.14.3
tqdm==4.67.1
decorator==4.4.2
joblib==1.5.1
h5py==3.14.0
proglog==0.1.12
DataProperty==1.1.0
nvidia-cuda-nvrtc-cu12==12.4.127
Jinja2==3.1.6
prompt_toolkit==3.0.51
pexpect==4.9.0
tcolorpy==0.1.7
scikit-learn==1.7.0
attrs==25.3.0
nvidia-curand-cu12==**********
MarkupSafe==3.0.2
iniconfig==2.1.0
nvidia-cuda-runtime-cu12==12.4.127
transformers==4.53.1
timm==1.0.16
hjson==3.1.0
idna==3.10
pytest==8.4.1
aiohttp==3.12.13
black==25.1.0
rich==14.0.0
protobuf==6.31.1
colorlog==6.9.0
colorama==0.4.6
torch==2.6.0
asttokens==3.0.0
shellingham==1.5.4
tabledata==1.3.4
charset-normalizer==3.4.2
qwen-vl-utils==0.0.11
multidict==6.6.3
nvidia-cusparse-cu12==**********
nvidia-nvjitlink-cu12==12.4.127
aiosignal==1.4.0
multiprocess==0.70.16
lighteval==0.10.0
einops==0.8.1
huggingface-hub==0.33.2
termcolor==2.3.0
networkx==3.4.2
sniffio==1.3.1
wcwidth==0.2.13
jedi==0.19.2
nvidia-cusolver-cu12==********
matplotlib-inline==0.1.7
pydantic==2.11.7
nvidia-cudnn-cu12==********
packaging==25.0
certifi==2025.7.9
smmap==5.0.2
propcache==0.3.2
pytz==2025.2
decord==0.6.0
accelerate==1.8.1
sentry-sdk==2.32.0
aenum==3.1.15
ipython==8.37.0
aiohappyeyeballs==2.6.1
pluggy==1.6.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
fsspec==2025.7.0
bitsandbytes==0.42.0
moviepy==1.0.3
six==1.17.0
pathvalidate==3.3.1
urllib3==2.5.0
mpmath==1.3.0
nvidia-cuda-cupti-cu12==12.4.127
pathspec==0.12.1
mccabe==0.7.0
py-cpuinfo==9.0.0
stack-data==0.6.3
isort==6.0.1
pandas==2.3.1
pillow==11.3.0
pycodestyle==2.14.0
python-dotenv==1.1.1
pyflakes==3.4.0
parameterized==0.9.0
python-dateutil==2.9.0.post0
tomli==2.2.1
pip==25.1.1
pip==25.1
exceptiongroup==1.3.0
PyYAML==6.0.2
typer==0.16.0
safetensors==0.5.3
tokenizers==0.21.2
parso==0.8.4
threadpoolctl==3.6.0
chardet==5.2.0
msgpack==1.1.1
wandb==0.21.0
sentencepiece==0.2.0
xxhash==3.5.0
psutil==7.0.0
regex==2024.11.6
nvidia-cublas-cu12==********
typepy==1.3.4
platformdirs==4.3.8
gitdb==4.0.12
math-verify==0.5.2
pytablewriter==1.2.1
tzdata==2025.2
deepspeed==0.15.4
async-timeout==5.0.1
frozenlist==1.7.0
GitPython==3.1.44
pfzy==0.3.4
mypy_extensions==1.1.0
ninja==********
mbstrdecoder==1.1.4
av==10.0.0
imageio-ffmpeg==0.6.0
typing-inspection==0.4.1
absl-py==2.3.1
wheel==0.45.1
rpds-py==0.26.0
referencing==0.36.2
jsonschema-specifications==2025.4.1
jsonschema==4.25.0
ray==2.48.0
omegaconf==2.3.0
orjson==3.11.0
cloudpickle==3.1.1
tensordict==0.7.2
triton==3.2.0
nvidia-cusparselt-cu12==0.6.2
typing_extensions==4.14.1
sympy==1.13.1
numpy==2.2.6
filelock==3.18.0
mathruler==0.1.0
pylatexenc==2.10
msgspec==0.19.0
cachetools==6.1.0
pyzmq==27.0.0
zmq==0.0.0
blake3==1.0.5
pybase64==1.4.1
latex2sympy2_extended==1.10.2
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
