{"time":"2025-07-31T23:14:09.706118389+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T23:14:09.843087011+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T23:14:09.843351786+08:00","level":"INFO","msg":"stream: created new stream","id":"s8o2uhm1"}
{"time":"2025-07-31T23:14:09.843394473+08:00","level":"INFO","msg":"stream: started","id":"s8o2uhm1"}
{"time":"2025-07-31T23:14:09.843486132+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"s8o2uhm1"}
{"time":"2025-07-31T23:14:09.843543786+08:00","level":"INFO","msg":"sender: started","stream_id":"s8o2uhm1"}
{"time":"2025-07-31T23:14:09.843556783+08:00","level":"INFO","msg":"handler: started","stream_id":"s8o2uhm1"}
{"time":"2025-07-31T23:14:09.844572192+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-07-31T23:16:39.249810922+08:00","level":"INFO","msg":"stream: closing","id":"s8o2uhm1"}
{"time":"2025-07-31T23:16:39.250451329+08:00","level":"INFO","msg":"handler: closed","stream_id":"s8o2uhm1"}
{"time":"2025-07-31T23:16:39.250508588+08:00","level":"INFO","msg":"sender: closed","stream_id":"s8o2uhm1"}
{"time":"2025-07-31T23:16:39.250503475+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"s8o2uhm1"}
{"time":"2025-07-31T23:16:39.250645154+08:00","level":"INFO","msg":"stream: closed","id":"s8o2uhm1"}
