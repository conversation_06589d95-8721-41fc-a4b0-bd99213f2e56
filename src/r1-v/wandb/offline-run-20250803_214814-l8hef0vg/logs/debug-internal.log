{"time":"2025-08-03T21:48:14.325713805+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T21:48:14.445231135+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T21:48:14.445459933+08:00","level":"INFO","msg":"stream: created new stream","id":"l8hef0vg"}
{"time":"2025-08-03T21:48:14.445499187+08:00","level":"INFO","msg":"stream: started","id":"l8hef0vg"}
{"time":"2025-08-03T21:48:14.445574334+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"l8hef0vg"}
{"time":"2025-08-03T21:48:14.445655214+08:00","level":"INFO","msg":"handler: started","stream_id":"l8hef0vg"}
{"time":"2025-08-03T21:48:14.445722422+08:00","level":"INFO","msg":"sender: started","stream_id":"l8hef0vg"}
{"time":"2025-08-03T21:48:14.446789535+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T21:50:41.73180147+08:00","level":"INFO","msg":"stream: closing","id":"l8hef0vg"}
{"time":"2025-08-03T21:50:41.732134041+08:00","level":"INFO","msg":"handler: closed","stream_id":"l8hef0vg"}
{"time":"2025-08-03T21:50:41.732188057+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"l8hef0vg"}
{"time":"2025-08-03T21:50:41.732195119+08:00","level":"INFO","msg":"sender: closed","stream_id":"l8hef0vg"}
{"time":"2025-08-03T21:50:41.732255601+08:00","level":"INFO","msg":"stream: closed","id":"l8hef0vg"}
