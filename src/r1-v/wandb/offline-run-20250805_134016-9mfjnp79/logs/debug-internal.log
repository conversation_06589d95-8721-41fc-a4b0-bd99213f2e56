{"time":"2025-08-05T13:40:16.365088536+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:40:16.497554706+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:40:16.497833502+08:00","level":"INFO","msg":"stream: created new stream","id":"9mfjnp79"}
{"time":"2025-08-05T13:40:16.497877705+08:00","level":"INFO","msg":"stream: started","id":"9mfjnp79"}
{"time":"2025-08-05T13:40:16.497963461+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"9mfjnp79"}
{"time":"2025-08-05T13:40:16.49808129+08:00","level":"INFO","msg":"sender: started","stream_id":"9mfjnp79"}
{"time":"2025-08-05T13:40:16.498120986+08:00","level":"INFO","msg":"handler: started","stream_id":"9mfjnp79"}
{"time":"2025-08-05T13:40:16.499298378+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T13:40:17.507321701+08:00","level":"INFO","msg":"stream: closing","id":"9mfjnp79"}
{"time":"2025-08-05T13:40:17.507629106+08:00","level":"INFO","msg":"handler: closed","stream_id":"9mfjnp79"}
{"time":"2025-08-05T13:40:17.507655416+08:00","level":"INFO","msg":"sender: closed","stream_id":"9mfjnp79"}
{"time":"2025-08-05T13:40:17.507655269+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"9mfjnp79"}
{"time":"2025-08-05T13:40:17.507789943+08:00","level":"INFO","msg":"stream: closed","id":"9mfjnp79"}
